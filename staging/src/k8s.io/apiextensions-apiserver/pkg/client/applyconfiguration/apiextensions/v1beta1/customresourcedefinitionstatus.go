/*
Copyright The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by applyconfiguration-gen. DO NOT EDIT.

package v1beta1

// CustomResourceDefinitionStatusApplyConfiguration represents an declarative configuration of the CustomResourceDefinitionStatus type for use
// with apply.
type CustomResourceDefinitionStatusApplyConfiguration struct {
	Conditions     []CustomResourceDefinitionConditionApplyConfiguration `json:"conditions,omitempty"`
	AcceptedNames  *CustomResourceDefinitionNamesApplyConfiguration      `json:"acceptedNames,omitempty"`
	StoredVersions []string                                              `json:"storedVersions,omitempty"`
}

// CustomResourceDefinitionStatusApplyConfiguration constructs an declarative configuration of the CustomResourceDefinitionStatus type for use with
// apply.
func CustomResourceDefinitionStatus() *CustomResourceDefinitionStatusApplyConfiguration {
	return &CustomResourceDefinitionStatusApplyConfiguration{}
}

// WithConditions adds the given value to the Conditions field in the declarative configuration
// and returns the receiver, so that objects can be build by chaining "With" function invocations.
// If called multiple times, values provided by each call will be appended to the Conditions field.
func (b *CustomResourceDefinitionStatusApplyConfiguration) WithConditions(values ...*CustomResourceDefinitionConditionApplyConfiguration) *CustomResourceDefinitionStatusApplyConfiguration {
	for i := range values {
		if values[i] == nil {
			panic("nil value passed to WithConditions")
		}
		b.Conditions = append(b.Conditions, *values[i])
	}
	return b
}

// WithAcceptedNames sets the AcceptedNames field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the AcceptedNames field is set to the value of the last call.
func (b *CustomResourceDefinitionStatusApplyConfiguration) WithAcceptedNames(value *CustomResourceDefinitionNamesApplyConfiguration) *CustomResourceDefinitionStatusApplyConfiguration {
	b.AcceptedNames = value
	return b
}

// WithStoredVersions adds the given value to the StoredVersions field in the declarative configuration
// and returns the receiver, so that objects can be build by chaining "With" function invocations.
// If called multiple times, values provided by each call will be appended to the StoredVersions field.
func (b *CustomResourceDefinitionStatusApplyConfiguration) WithStoredVersions(values ...string) *CustomResourceDefinitionStatusApplyConfiguration {
	for i := range values {
		b.StoredVersions = append(b.StoredVersions, values[i])
	}
	return b
}
