/*
Copyright The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by applyconfiguration-gen. DO NOT EDIT.

package v1beta1

// SelectableFieldApplyConfiguration represents an declarative configuration of the SelectableField type for use
// with apply.
type SelectableFieldApplyConfiguration struct {
	JSONPath *string `json:"jsonPath,omitempty"`
}

// SelectableFieldApplyConfiguration constructs an declarative configuration of the SelectableField type for use with
// apply.
func SelectableField() *SelectableFieldApplyConfiguration {
	return &SelectableFieldApplyConfiguration{}
}

// WithJSONPath sets the JSONPath field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the JSONPath field is set to the value of the last call.
func (b *SelectableFieldApplyConfiguration) WithJSONPath(value string) *SelectableFieldApplyConfiguration {
	b.JSONPath = &value
	return b
}
