/*
Copyright The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by applyconfiguration-gen. DO NOT EDIT.

package v1

// WebhookConversionApplyConfiguration represents an declarative configuration of the WebhookConversion type for use
// with apply.
type WebhookConversionApplyConfiguration struct {
	ClientConfig             *WebhookClientConfigApplyConfiguration `json:"clientConfig,omitempty"`
	ConversionReviewVersions []string                               `json:"conversionReviewVersions,omitempty"`
}

// WebhookConversionApplyConfiguration constructs an declarative configuration of the WebhookConversion type for use with
// apply.
func WebhookConversion() *WebhookConversionApplyConfiguration {
	return &WebhookConversionApplyConfiguration{}
}

// WithClientConfig sets the ClientConfig field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the ClientConfig field is set to the value of the last call.
func (b *WebhookConversionApplyConfiguration) WithClientConfig(value *WebhookClientConfigApplyConfiguration) *WebhookConversionApplyConfiguration {
	b.ClientConfig = value
	return b
}

// WithConversionReviewVersions adds the given value to the ConversionReviewVersions field in the declarative configuration
// and returns the receiver, so that objects can be build by chaining "With" function invocations.
// If called multiple times, values provided by each call will be appended to the ConversionReviewVersions field.
func (b *WebhookConversionApplyConfiguration) WithConversionReviewVersions(values ...string) *WebhookConversionApplyConfiguration {
	for i := range values {
		b.ConversionReviewVersions = append(b.ConversionReviewVersions, values[i])
	}
	return b
}
