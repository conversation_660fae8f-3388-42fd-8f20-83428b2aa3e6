/*
Copyright The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by applyconfiguration-gen. DO NOT EDIT.

package v1

// ExternalDocumentationApplyConfiguration represents an declarative configuration of the ExternalDocumentation type for use
// with apply.
type ExternalDocumentationApplyConfiguration struct {
	Description *string `json:"description,omitempty"`
	URL         *string `json:"url,omitempty"`
}

// ExternalDocumentationApplyConfiguration constructs an declarative configuration of the ExternalDocumentation type for use with
// apply.
func ExternalDocumentation() *ExternalDocumentationApplyConfiguration {
	return &ExternalDocumentationApplyConfiguration{}
}

// WithDescription sets the Description field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Description field is set to the value of the last call.
func (b *ExternalDocumentationApplyConfiguration) WithDescription(value string) *ExternalDocumentationApplyConfiguration {
	b.Description = &value
	return b
}

// WithURL sets the URL field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the URL field is set to the value of the last call.
func (b *ExternalDocumentationApplyConfiguration) WithURL(value string) *ExternalDocumentationApplyConfiguration {
	b.URL = &value
	return b
}
