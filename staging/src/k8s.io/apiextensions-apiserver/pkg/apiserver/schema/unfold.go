/*
Copyright 2019 The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package schema

// Unfold expands vendor extensions of a structural schema.
// It mutates the receiver.
func (s *Structural) Unfold() *Structural {
	if s == nil {
		return nil
	}

	mapper := Visitor{
		Structural: func(s *Structural) bool {
			if !s.XIntOrString {
				return false
			}

			skipAnyOf := isIntOrStringAnyOfPattern(s)
			skipFirstAllOfAnyOf := isIntOrStringAllOfPattern(s)
			if skipAnyOf || skipFirstAllOfAnyOf {
				return false
			}

			if s.ValueValidation == nil {
				s.ValueValidation = &ValueValidation{}
			}
			if s.ValueValidation.AnyOf == nil {
				s.ValueValidation.AnyOf = []NestedValueValidation{
					{ForbiddenGenerics: Generic{Type: "integer"}},
					{ForbiddenGenerics: Generic{Type: "string"}},
				}
			} else {
				s.ValueValidation.AllOf = append([]NestedValueValidation{
					{
						ValueValidation: ValueValidation{
							AnyOf: []NestedValueValidation{
								{ForbiddenGenerics: Generic{Type: "integer"}},
								{ForbiddenGenerics: Generic{Type: "string"}},
							},
						},
					},
				}, s.ValueValidation.AllOf...)
			}

			return true
		},
		NestedValueValidation: nil, // x-kubernetes-int-or-string cannot be set in nested value validation
	}
	mapper.Visit(s)

	return s
}
