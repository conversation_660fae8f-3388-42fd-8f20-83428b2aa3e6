/*
Copyright The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by applyconfiguration-gen. DO NOT EDIT.

package v1

// ExampleSpecApplyConfiguration represents an declarative configuration of the ExampleSpec type for use
// with apply.
type ExampleSpecApplyConfiguration struct {
	Foo *string `json:"foo,omitempty"`
	Bar *bool   `json:"bar,omitempty"`
}

// ExampleSpecApplyConfiguration constructs an declarative configuration of the ExampleSpec type for use with
// apply.
func ExampleSpec() *ExampleSpecApplyConfiguration {
	return &ExampleSpecApplyConfiguration{}
}

// WithFoo sets the Foo field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Foo field is set to the value of the last call.
func (b *ExampleSpecApplyConfiguration) WithFoo(value string) *ExampleSpecApplyConfiguration {
	b.Foo = &value
	return b
}

// WithBar sets the Bar field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Bar field is set to the value of the last call.
func (b *ExampleSpecApplyConfiguration) WithBar(value bool) *ExampleSpecApplyConfiguration {
	b.Bar = &value
	return b
}
