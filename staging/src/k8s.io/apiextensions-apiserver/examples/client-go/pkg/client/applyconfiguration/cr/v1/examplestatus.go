/*
Copyright The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by applyconfiguration-gen. DO NOT EDIT.

package v1

import (
	v1 "k8s.io/apiextensions-apiserver/examples/client-go/pkg/apis/cr/v1"
)

// ExampleStatusApplyConfiguration represents an declarative configuration of the ExampleStatus type for use
// with apply.
type ExampleStatusApplyConfiguration struct {
	State   *v1.ExampleState `json:"state,omitempty"`
	Message *string          `json:"message,omitempty"`
}

// ExampleStatusApplyConfiguration constructs an declarative configuration of the ExampleStatus type for use with
// apply.
func ExampleStatus() *ExampleStatusApplyConfiguration {
	return &ExampleStatusApplyConfiguration{}
}

// WithState sets the State field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the State field is set to the value of the last call.
func (b *ExampleStatusApplyConfiguration) WithState(value v1.ExampleState) *ExampleStatusApplyConfiguration {
	b.State = &value
	return b
}

// WithMessage sets the Message field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Message field is set to the value of the last call.
func (b *ExampleStatusApplyConfiguration) WithMessage(value string) *ExampleStatusApplyConfiguration {
	b.Message = &value
	return b
}
