/*
Copyright The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by client-gen. DO NOT EDIT.

package fake

import (
	"context"
	json "encoding/json"
	"fmt"

	v1 "k8s.io/apiextensions-apiserver/examples/client-go/pkg/apis/cr/v1"
	crv1 "k8s.io/apiextensions-apiserver/examples/client-go/pkg/client/applyconfiguration/cr/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	labels "k8s.io/apimachinery/pkg/labels"
	types "k8s.io/apimachinery/pkg/types"
	watch "k8s.io/apimachinery/pkg/watch"
	testing "k8s.io/client-go/testing"
)

// FakeExamples implements ExampleInterface
type FakeExamples struct {
	Fake *FakeCrV1
	ns   string
}

var examplesResource = v1.SchemeGroupVersion.WithResource("examples")

var examplesKind = v1.SchemeGroupVersion.WithKind("Example")

// Get takes name of the example, and returns the corresponding example object, and an error if there is any.
func (c *FakeExamples) Get(ctx context.Context, name string, options metav1.GetOptions) (result *v1.Example, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewGetAction(examplesResource, c.ns, name), &v1.Example{})

	if obj == nil {
		return nil, err
	}
	return obj.(*v1.Example), err
}

// List takes label and field selectors, and returns the list of Examples that match those selectors.
func (c *FakeExamples) List(ctx context.Context, opts metav1.ListOptions) (result *v1.ExampleList, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewListAction(examplesResource, examplesKind, c.ns, opts), &v1.ExampleList{})

	if obj == nil {
		return nil, err
	}

	label, _, _ := testing.ExtractFromListOptions(opts)
	if label == nil {
		label = labels.Everything()
	}
	list := &v1.ExampleList{ListMeta: obj.(*v1.ExampleList).ListMeta}
	for _, item := range obj.(*v1.ExampleList).Items {
		if label.Matches(labels.Set(item.Labels)) {
			list.Items = append(list.Items, item)
		}
	}
	return list, err
}

// Watch returns a watch.Interface that watches the requested examples.
func (c *FakeExamples) Watch(ctx context.Context, opts metav1.ListOptions) (watch.Interface, error) {
	return c.Fake.
		InvokesWatch(testing.NewWatchAction(examplesResource, c.ns, opts))

}

// Create takes the representation of a example and creates it.  Returns the server's representation of the example, and an error, if there is any.
func (c *FakeExamples) Create(ctx context.Context, example *v1.Example, opts metav1.CreateOptions) (result *v1.Example, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewCreateAction(examplesResource, c.ns, example), &v1.Example{})

	if obj == nil {
		return nil, err
	}
	return obj.(*v1.Example), err
}

// Update takes the representation of a example and updates it. Returns the server's representation of the example, and an error, if there is any.
func (c *FakeExamples) Update(ctx context.Context, example *v1.Example, opts metav1.UpdateOptions) (result *v1.Example, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewUpdateAction(examplesResource, c.ns, example), &v1.Example{})

	if obj == nil {
		return nil, err
	}
	return obj.(*v1.Example), err
}

// Delete takes name of the example and deletes it. Returns an error if one occurs.
func (c *FakeExamples) Delete(ctx context.Context, name string, opts metav1.DeleteOptions) error {
	_, err := c.Fake.
		Invokes(testing.NewDeleteActionWithOptions(examplesResource, c.ns, name, opts), &v1.Example{})

	return err
}

// DeleteCollection deletes a collection of objects.
func (c *FakeExamples) DeleteCollection(ctx context.Context, opts metav1.DeleteOptions, listOpts metav1.ListOptions) error {
	action := testing.NewDeleteCollectionAction(examplesResource, c.ns, listOpts)

	_, err := c.Fake.Invokes(action, &v1.ExampleList{})
	return err
}

// Patch applies the patch and returns the patched example.
func (c *FakeExamples) Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts metav1.PatchOptions, subresources ...string) (result *v1.Example, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewPatchSubresourceAction(examplesResource, c.ns, name, pt, data, subresources...), &v1.Example{})

	if obj == nil {
		return nil, err
	}
	return obj.(*v1.Example), err
}

// Apply takes the given apply declarative configuration, applies it and returns the applied example.
func (c *FakeExamples) Apply(ctx context.Context, example *crv1.ExampleApplyConfiguration, opts metav1.ApplyOptions) (result *v1.Example, err error) {
	if example == nil {
		return nil, fmt.Errorf("example provided to Apply must not be nil")
	}
	data, err := json.Marshal(example)
	if err != nil {
		return nil, err
	}
	name := example.Name
	if name == nil {
		return nil, fmt.Errorf("example.Name must be provided to Apply")
	}
	obj, err := c.Fake.
		Invokes(testing.NewPatchSubresourceAction(examplesResource, c.ns, *name, types.ApplyPatchType, data), &v1.Example{})

	if obj == nil {
		return nil, err
	}
	return obj.(*v1.Example), err
}
