apiVersion: gateway.networking.k8s.io/v1beta1
kind: HTTPRoute
metadata:
  name: header-http-echo
spec:
  parentRefs:
    - name: acme-gw
  rules:
    - matches:
        - path:
            type: PathPrefix
            value: /remove-a-request-header
      filters:
        - type: RequestHeaderModifier
          requestHeaderModifier:
            remove:
              - x-request-id
      backendRefs:
        - name: echo
          port: 8080
