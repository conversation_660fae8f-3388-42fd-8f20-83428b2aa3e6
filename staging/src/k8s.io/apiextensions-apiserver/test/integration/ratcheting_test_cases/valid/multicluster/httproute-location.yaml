#$ Used in:
#$ - geps/gep-1748.md
kind: HTTPRoute
apiVersion: gateway.networking.k8s.io/v1beta1
metadata:
  name: store
spec:
  parentRefs:
  - name: external-http
  rules:
  - matches:
    - path:
        type: PathPrefix
        value: /west
    backendRefs:
    - group: multicluster.x-k8s.io
      kind: ServiceImport
      name: store-west
      port: 8080
  - matches:
    - path:
        type: PathPrefix
        value: /east
    backendRefs:
    - group: multicluster.x-k8s.io
      kind: ServiceImport
      name: store-east
      port: 8080
  - backendRefs:
    - group: multicluster.x-k8s.io
      kind: ServiceImport
      name: store
      port: 8080
