apiVersion: gateway.networking.k8s.io/v1beta1
kind: Gateway
metadata:
  name: gateway-addresses
spec:
  gatewayClassName: acme-lb
  addresses:
  - value: 1200:0000:AB00:1234:0000:2552:7777:1313
  - value: 21DA:D3:0:2F3B:2AA:FF:FE28:9C5A
  - value: "2001:db8:3c4d:15:0:d234:3eee::"
  - value: "1234::"
  - value: "*******"
  - value: "*******"
  - value: "0.0.0.0"
  - value: "*************"
  - value: "********"
  - type: IPAddress
    value: "***************"
  - type: "Hostname"
    value: "example.com"
  listeners:
  - protocol: HTTP
    port: 80
    name: prod-web-gw
    allowedRoutes:
      namespaces:
        from: Same
