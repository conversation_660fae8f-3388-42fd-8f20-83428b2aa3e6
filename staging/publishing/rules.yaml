rules:
- destination: apimachinery
  branches:
  - name: master
    source:
      branch: master
      dirs:
      - staging/src/k8s.io/apimachinery
  - name: release-1.26
    go: 1.21.9
    source:
      branch: release-1.26
      dirs:
      - staging/src/k8s.io/apimachinery
  - name: release-1.27
    go: 1.21.9
    source:
      branch: release-1.27
      dirs:
      - staging/src/k8s.io/apimachinery
  - name: release-1.28
    go: 1.21.9
    source:
      branch: release-1.28
      dirs:
      - staging/src/k8s.io/apimachinery
  - name: release-1.29
    go: 1.21.9
    source:
      branch: release-1.29
      dirs:
      - staging/src/k8s.io/apimachinery
  - name: release-1.30
    go: 1.22.2
    source:
      branch: release-1.30
      dirs:
      - staging/src/k8s.io/apimachinery
  library: true
- destination: api
  branches:
  - name: master
    dependencies:
    - repository: apimachinery
      branch: master
    source:
      branch: master
      dirs:
      - staging/src/k8s.io/api
  - name: release-1.26
    go: 1.21.9
    dependencies:
    - repository: apimachinery
      branch: release-1.26
    source:
      branch: release-1.26
      dirs:
      - staging/src/k8s.io/api
  - name: release-1.27
    go: 1.21.9
    dependencies:
    - repository: apimachinery
      branch: release-1.27
    source:
      branch: release-1.27
      dirs:
      - staging/src/k8s.io/api
  - name: release-1.28
    go: 1.21.9
    dependencies:
    - repository: apimachinery
      branch: release-1.28
    source:
      branch: release-1.28
      dirs:
      - staging/src/k8s.io/api
  - name: release-1.29
    go: 1.21.9
    dependencies:
    - repository: apimachinery
      branch: release-1.29
    source:
      branch: release-1.29
      dirs:
      - staging/src/k8s.io/api
  - name: release-1.30
    go: 1.22.2
    dependencies:
    - repository: apimachinery
      branch: release-1.30
    source:
      branch: release-1.30
      dirs:
      - staging/src/k8s.io/api
  library: true
- destination: client-go
  branches:
  - name: master
    dependencies:
    - repository: apimachinery
      branch: master
    - repository: api
      branch: master
    source:
      branch: master
      dirs:
      - staging/src/k8s.io/client-go
    smoke-test: |
      # assumes GO111MODULE=on
      go build -mod=mod ./...
      go test -mod=mod ./...
  - name: release-1.26
    go: 1.21.9
    dependencies:
    - repository: apimachinery
      branch: release-1.26
    - repository: api
      branch: release-1.26
    source:
      branch: release-1.26
      dirs:
      - staging/src/k8s.io/client-go
    smoke-test: |
      # assumes GO111MODULE=on
      go build -mod=mod ./...
      go test -mod=mod ./...
  - name: release-1.27
    go: 1.21.9
    dependencies:
    - repository: apimachinery
      branch: release-1.27
    - repository: api
      branch: release-1.27
    source:
      branch: release-1.27
      dirs:
      - staging/src/k8s.io/client-go
    smoke-test: |
      # assumes GO111MODULE=on
      go build -mod=mod ./...
      go test -mod=mod ./...
  - name: release-1.28
    go: 1.21.9
    dependencies:
    - repository: apimachinery
      branch: release-1.28
    - repository: api
      branch: release-1.28
    source:
      branch: release-1.28
      dirs:
      - staging/src/k8s.io/client-go
    smoke-test: |
      # assumes GO111MODULE=on
      go build -mod=mod ./...
      go test -mod=mod ./...
  - name: release-1.29
    go: 1.21.9
    dependencies:
    - repository: apimachinery
      branch: release-1.29
    - repository: api
      branch: release-1.29
    source:
      branch: release-1.29
      dirs:
      - staging/src/k8s.io/client-go
    smoke-test: |
      # assumes GO111MODULE=on
      go build -mod=mod ./...
      go test -mod=mod ./...
  - name: release-1.30
    go: 1.22.2
    dependencies:
    - repository: apimachinery
      branch: release-1.30
    - repository: api
      branch: release-1.30
    source:
      branch: release-1.30
      dirs:
      - staging/src/k8s.io/client-go
    smoke-test: |
      # assumes GO111MODULE=on
      go build -mod=mod ./...
      go test -mod=mod ./...
  library: true
- destination: code-generator
  branches:
  - name: master
    dependencies:
    - repository: apimachinery
      branch: master
    source:
      branch: master
      dirs:
      - staging/src/k8s.io/code-generator
  - name: release-1.26
    go: 1.21.9
    source:
      branch: release-1.26
      dirs:
      - staging/src/k8s.io/code-generator
  - name: release-1.27
    go: 1.21.9
    source:
      branch: release-1.27
      dirs:
      - staging/src/k8s.io/code-generator
  - name: release-1.28
    go: 1.21.9
    source:
      branch: release-1.28
      dirs:
      - staging/src/k8s.io/code-generator
  - name: release-1.29
    go: 1.21.9
    source:
      branch: release-1.29
      dirs:
      - staging/src/k8s.io/code-generator
  - name: release-1.30
    go: 1.22.2
    dependencies:
    - repository: apimachinery
      branch: release-1.30
    source:
      branch: release-1.30
      dirs:
      - staging/src/k8s.io/code-generator
- destination: component-base
  branches:
  - name: master
    dependencies:
    - repository: apimachinery
      branch: master
    - repository: api
      branch: master
    - repository: client-go
      branch: master
    source:
      branch: master
      dirs:
      - staging/src/k8s.io/component-base
  - name: release-1.26
    go: 1.21.9
    dependencies:
    - repository: apimachinery
      branch: release-1.26
    - repository: api
      branch: release-1.26
    - repository: client-go
      branch: release-1.26
    source:
      branch: release-1.26
      dirs:
      - staging/src/k8s.io/component-base
  - name: release-1.27
    go: 1.21.9
    dependencies:
    - repository: apimachinery
      branch: release-1.27
    - repository: api
      branch: release-1.27
    - repository: client-go
      branch: release-1.27
    source:
      branch: release-1.27
      dirs:
      - staging/src/k8s.io/component-base
  - name: release-1.28
    go: 1.21.9
    dependencies:
    - repository: apimachinery
      branch: release-1.28
    - repository: api
      branch: release-1.28
    - repository: client-go
      branch: release-1.28
    source:
      branch: release-1.28
      dirs:
      - staging/src/k8s.io/component-base
  - name: release-1.29
    go: 1.21.9
    dependencies:
    - repository: apimachinery
      branch: release-1.29
    - repository: api
      branch: release-1.29
    - repository: client-go
      branch: release-1.29
    source:
      branch: release-1.29
      dirs:
      - staging/src/k8s.io/component-base
  - name: release-1.30
    go: 1.22.2
    dependencies:
    - repository: apimachinery
      branch: release-1.30
    - repository: api
      branch: release-1.30
    - repository: client-go
      branch: release-1.30
    source:
      branch: release-1.30
      dirs:
      - staging/src/k8s.io/component-base
  library: true
- destination: component-helpers
  branches:
  - name: master
    dependencies:
    - repository: apimachinery
      branch: master
    - repository: api
      branch: master
    - repository: client-go
      branch: master
    source:
      branch: master
      dirs:
      - staging/src/k8s.io/component-helpers
  - name: release-1.26
    go: 1.21.9
    dependencies:
    - repository: apimachinery
      branch: release-1.26
    - repository: api
      branch: release-1.26
    - repository: client-go
      branch: release-1.26
    source:
      branch: release-1.26
      dirs:
      - staging/src/k8s.io/component-helpers
  - name: release-1.27
    go: 1.21.9
    dependencies:
    - repository: apimachinery
      branch: release-1.27
    - repository: api
      branch: release-1.27
    - repository: client-go
      branch: release-1.27
    source:
      branch: release-1.27
      dirs:
      - staging/src/k8s.io/component-helpers
  - name: release-1.28
    go: 1.21.9
    dependencies:
    - repository: apimachinery
      branch: release-1.28
    - repository: api
      branch: release-1.28
    - repository: client-go
      branch: release-1.28
    source:
      branch: release-1.28
      dirs:
      - staging/src/k8s.io/component-helpers
  - name: release-1.29
    go: 1.21.9
    dependencies:
    - repository: apimachinery
      branch: release-1.29
    - repository: api
      branch: release-1.29
    - repository: client-go
      branch: release-1.29
    source:
      branch: release-1.29
      dirs:
      - staging/src/k8s.io/component-helpers
  - name: release-1.30
    go: 1.22.2
    dependencies:
    - repository: apimachinery
      branch: release-1.30
    - repository: api
      branch: release-1.30
    - repository: client-go
      branch: release-1.30
    source:
      branch: release-1.30
      dirs:
      - staging/src/k8s.io/component-helpers
  library: true
- destination: kms
  branches:
  - name: master
    dependencies:
    - repository: apimachinery
      branch: master
    source:
      branch: master
      dirs:
      - staging/src/k8s.io/kms
  - name: release-1.26
    go: 1.21.9
    source:
      branch: release-1.26
      dirs:
      - staging/src/k8s.io/kms
  - name: release-1.27
    go: 1.21.9
    dependencies:
    - repository: apimachinery
      branch: release-1.27
    - repository: api
      branch: release-1.27
    - repository: client-go
      branch: release-1.27
    source:
      branch: release-1.27
      dirs:
      - staging/src/k8s.io/kms
  - name: release-1.28
    go: 1.21.9
    dependencies:
    - repository: apimachinery
      branch: release-1.28
    - repository: api
      branch: release-1.28
    - repository: client-go
      branch: release-1.28
    source:
      branch: release-1.28
      dirs:
      - staging/src/k8s.io/kms
  - name: release-1.29
    go: 1.21.9
    dependencies:
    - repository: apimachinery
      branch: release-1.29
    source:
      branch: release-1.29
      dirs:
      - staging/src/k8s.io/kms
  - name: release-1.30
    go: 1.22.2
    dependencies:
    - repository: apimachinery
      branch: release-1.30
    source:
      branch: release-1.30
      dirs:
      - staging/src/k8s.io/kms
  library: true
- destination: apiserver
  branches:
  - name: master
    dependencies:
    - repository: apimachinery
      branch: master
    - repository: api
      branch: master
    - repository: client-go
      branch: master
    - repository: component-base
      branch: master
    - repository: kms
      branch: master
    source:
      branch: master
      dirs:
      - staging/src/k8s.io/apiserver
  - name: release-1.26
    go: 1.21.9
    dependencies:
    - repository: apimachinery
      branch: release-1.26
    - repository: api
      branch: release-1.26
    - repository: client-go
      branch: release-1.26
    - repository: component-base
      branch: release-1.26
    - repository: kms
      branch: release-1.26
    source:
      branch: release-1.26
      dirs:
      - staging/src/k8s.io/apiserver
  - name: release-1.27
    go: 1.21.9
    dependencies:
    - repository: apimachinery
      branch: release-1.27
    - repository: api
      branch: release-1.27
    - repository: client-go
      branch: release-1.27
    - repository: component-base
      branch: release-1.27
    - repository: kms
      branch: release-1.27
    source:
      branch: release-1.27
      dirs:
      - staging/src/k8s.io/apiserver
  - name: release-1.28
    go: 1.21.9
    dependencies:
    - repository: apimachinery
      branch: release-1.28
    - repository: api
      branch: release-1.28
    - repository: client-go
      branch: release-1.28
    - repository: component-base
      branch: release-1.28
    - repository: kms
      branch: release-1.28
    source:
      branch: release-1.28
      dirs:
      - staging/src/k8s.io/apiserver
  - name: release-1.29
    go: 1.21.9
    dependencies:
    - repository: apimachinery
      branch: release-1.29
    - repository: api
      branch: release-1.29
    - repository: client-go
      branch: release-1.29
    - repository: component-base
      branch: release-1.29
    - repository: kms
      branch: release-1.29
    source:
      branch: release-1.29
      dirs:
      - staging/src/k8s.io/apiserver
  - name: release-1.30
    go: 1.22.2
    dependencies:
    - repository: apimachinery
      branch: release-1.30
    - repository: api
      branch: release-1.30
    - repository: client-go
      branch: release-1.30
    - repository: component-base
      branch: release-1.30
    - repository: kms
      branch: release-1.30
    source:
      branch: release-1.30
      dirs:
      - staging/src/k8s.io/apiserver
  library: true
- destination: kube-aggregator
  branches:
  - name: master
    dependencies:
    - repository: apimachinery
      branch: master
    - repository: api
      branch: master
    - repository: client-go
      branch: master
    - repository: apiserver
      branch: master
    - repository: component-base
      branch: master
    - repository: kms
      branch: master
    - repository: code-generator
      branch: master
    source:
      branch: master
      dirs:
      - staging/src/k8s.io/kube-aggregator
  - name: release-1.26
    go: 1.21.9
    dependencies:
    - repository: apimachinery
      branch: release-1.26
    - repository: api
      branch: release-1.26
    - repository: client-go
      branch: release-1.26
    - repository: apiserver
      branch: release-1.26
    - repository: component-base
      branch: release-1.26
    - repository: code-generator
      branch: release-1.26
    - repository: kms
      branch: release-1.26
    source:
      branch: release-1.26
      dirs:
      - staging/src/k8s.io/kube-aggregator
  - name: release-1.27
    go: 1.21.9
    dependencies:
    - repository: apimachinery
      branch: release-1.27
    - repository: api
      branch: release-1.27
    - repository: client-go
      branch: release-1.27
    - repository: apiserver
      branch: release-1.27
    - repository: component-base
      branch: release-1.27
    - repository: kms
      branch: release-1.27
    - repository: code-generator
      branch: release-1.27
    source:
      branch: release-1.27
      dirs:
      - staging/src/k8s.io/kube-aggregator
  - name: release-1.28
    go: 1.21.9
    dependencies:
    - repository: apimachinery
      branch: release-1.28
    - repository: api
      branch: release-1.28
    - repository: client-go
      branch: release-1.28
    - repository: apiserver
      branch: release-1.28
    - repository: component-base
      branch: release-1.28
    - repository: kms
      branch: release-1.28
    - repository: code-generator
      branch: release-1.28
    source:
      branch: release-1.28
      dirs:
      - staging/src/k8s.io/kube-aggregator
  - name: release-1.29
    go: 1.21.9
    dependencies:
    - repository: apimachinery
      branch: release-1.29
    - repository: api
      branch: release-1.29
    - repository: client-go
      branch: release-1.29
    - repository: apiserver
      branch: release-1.29
    - repository: component-base
      branch: release-1.29
    - repository: kms
      branch: release-1.29
    - repository: code-generator
      branch: release-1.29
    source:
      branch: release-1.29
      dirs:
      - staging/src/k8s.io/kube-aggregator
  - name: release-1.30
    go: 1.22.2
    dependencies:
    - repository: apimachinery
      branch: release-1.30
    - repository: api
      branch: release-1.30
    - repository: client-go
      branch: release-1.30
    - repository: apiserver
      branch: release-1.30
    - repository: component-base
      branch: release-1.30
    - repository: kms
      branch: release-1.30
    - repository: code-generator
      branch: release-1.30
    source:
      branch: release-1.30
      dirs:
      - staging/src/k8s.io/kube-aggregator
- destination: sample-apiserver
  branches:
  - name: master
    dependencies:
    - repository: apimachinery
      branch: master
    - repository: api
      branch: master
    - repository: client-go
      branch: master
    - repository: apiserver
      branch: master
    - repository: code-generator
      branch: master
    - repository: kms
      branch: master
    - repository: component-base
      branch: master
    source:
      branch: master
      dirs:
      - staging/src/k8s.io/sample-apiserver
    required-packages:
    - k8s.io/code-generator
    smoke-test: |
      # assumes GO111MODULE=on
      go build -mod=mod .
  - name: release-1.26
    go: 1.21.9
    dependencies:
    - repository: apimachinery
      branch: release-1.26
    - repository: api
      branch: release-1.26
    - repository: client-go
      branch: release-1.26
    - repository: apiserver
      branch: release-1.26
    - repository: code-generator
      branch: release-1.26
    - repository: component-base
      branch: release-1.26
    - repository: kms
      branch: release-1.26
    source:
      branch: release-1.26
      dirs:
      - staging/src/k8s.io/sample-apiserver
    required-packages:
    - k8s.io/code-generator
    smoke-test: |
      # assumes GO111MODULE=on
      go build -mod=mod .
  - name: release-1.27
    go: 1.21.9
    dependencies:
    - repository: apimachinery
      branch: release-1.27
    - repository: api
      branch: release-1.27
    - repository: client-go
      branch: release-1.27
    - repository: apiserver
      branch: release-1.27
    - repository: code-generator
      branch: release-1.27
    - repository: kms
      branch: release-1.27
    - repository: component-base
      branch: release-1.27
    source:
      branch: release-1.27
      dirs:
      - staging/src/k8s.io/sample-apiserver
    required-packages:
    - k8s.io/code-generator
    smoke-test: |
      # assumes GO111MODULE=on
      go build -mod=mod .
  - name: release-1.28
    go: 1.21.9
    dependencies:
    - repository: apimachinery
      branch: release-1.28
    - repository: api
      branch: release-1.28
    - repository: client-go
      branch: release-1.28
    - repository: apiserver
      branch: release-1.28
    - repository: code-generator
      branch: release-1.28
    - repository: kms
      branch: release-1.28
    - repository: component-base
      branch: release-1.28
    source:
      branch: release-1.28
      dirs:
      - staging/src/k8s.io/sample-apiserver
    required-packages:
    - k8s.io/code-generator
    smoke-test: |
      # assumes GO111MODULE=on
      go build -mod=mod .
  - name: release-1.29
    go: 1.21.9
    dependencies:
    - repository: apimachinery
      branch: release-1.29
    - repository: api
      branch: release-1.29
    - repository: client-go
      branch: release-1.29
    - repository: apiserver
      branch: release-1.29
    - repository: code-generator
      branch: release-1.29
    - repository: kms
      branch: release-1.29
    - repository: component-base
      branch: release-1.29
    source:
      branch: release-1.29
      dirs:
      - staging/src/k8s.io/sample-apiserver
    required-packages:
    - k8s.io/code-generator
    smoke-test: |
      # assumes GO111MODULE=on
      go build -mod=mod .
  - name: release-1.30
    go: 1.22.2
    dependencies:
    - repository: apimachinery
      branch: release-1.30
    - repository: api
      branch: release-1.30
    - repository: client-go
      branch: release-1.30
    - repository: apiserver
      branch: release-1.30
    - repository: code-generator
      branch: release-1.30
    - repository: kms
      branch: release-1.30
    - repository: component-base
      branch: release-1.30
    source:
      branch: release-1.30
      dirs:
      - staging/src/k8s.io/sample-apiserver
    required-packages:
    - k8s.io/code-generator
    smoke-test: |
      # assumes GO111MODULE=on
      go build -mod=mod .
- destination: sample-controller
  branches:
  - name: master
    dependencies:
    - repository: apimachinery
      branch: master
    - repository: api
      branch: master
    - repository: client-go
      branch: master
    - repository: code-generator
      branch: master
    source:
      branch: master
      dirs:
      - staging/src/k8s.io/sample-controller
    required-packages:
    - k8s.io/code-generator
    smoke-test: |
      # assumes GO111MODULE=on
      go build -mod=mod .
  - name: release-1.26
    go: 1.21.9
    dependencies:
    - repository: apimachinery
      branch: release-1.26
    - repository: api
      branch: release-1.26
    - repository: client-go
      branch: release-1.26
    - repository: code-generator
      branch: release-1.26
    source:
      branch: release-1.26
      dirs:
      - staging/src/k8s.io/sample-controller
    required-packages:
    - k8s.io/code-generator
    smoke-test: |
      # assumes GO111MODULE=on
      go build -mod=mod .
  - name: release-1.27
    go: 1.21.9
    dependencies:
    - repository: apimachinery
      branch: release-1.27
    - repository: api
      branch: release-1.27
    - repository: client-go
      branch: release-1.27
    - repository: code-generator
      branch: release-1.27
    source:
      branch: release-1.27
      dirs:
      - staging/src/k8s.io/sample-controller
    required-packages:
    - k8s.io/code-generator
    smoke-test: |
      # assumes GO111MODULE=on
      go build -mod=mod .
  - name: release-1.28
    go: 1.21.9
    dependencies:
    - repository: apimachinery
      branch: release-1.28
    - repository: api
      branch: release-1.28
    - repository: client-go
      branch: release-1.28
    - repository: code-generator
      branch: release-1.28
    source:
      branch: release-1.28
      dirs:
      - staging/src/k8s.io/sample-controller
    required-packages:
    - k8s.io/code-generator
    smoke-test: |
      # assumes GO111MODULE=on
      go build -mod=mod .
  - name: release-1.29
    go: 1.21.9
    dependencies:
    - repository: apimachinery
      branch: release-1.29
    - repository: api
      branch: release-1.29
    - repository: client-go
      branch: release-1.29
    - repository: code-generator
      branch: release-1.29
    source:
      branch: release-1.29
      dirs:
      - staging/src/k8s.io/sample-controller
    required-packages:
    - k8s.io/code-generator
    smoke-test: |
      # assumes GO111MODULE=on
      go build -mod=mod .
  - name: release-1.30
    go: 1.22.2
    dependencies:
    - repository: apimachinery
      branch: release-1.30
    - repository: api
      branch: release-1.30
    - repository: client-go
      branch: release-1.30
    - repository: code-generator
      branch: release-1.30
    source:
      branch: release-1.30
      dirs:
      - staging/src/k8s.io/sample-controller
    required-packages:
    - k8s.io/code-generator
    smoke-test: |
      # assumes GO111MODULE=on
      go build -mod=mod .
- destination: apiextensions-apiserver
  branches:
  - name: master
    dependencies:
    - repository: apimachinery
      branch: master
    - repository: api
      branch: master
    - repository: client-go
      branch: master
    - repository: apiserver
      branch: master
    - repository: code-generator
      branch: master
    - repository: component-base
      branch: master
    - repository: kms
      branch: master
    source:
      branch: master
      dirs:
      - staging/src/k8s.io/apiextensions-apiserver
    required-packages:
    - k8s.io/code-generator
  - name: release-1.26
    go: 1.21.9
    dependencies:
    - repository: apimachinery
      branch: release-1.26
    - repository: api
      branch: release-1.26
    - repository: client-go
      branch: release-1.26
    - repository: apiserver
      branch: release-1.26
    - repository: code-generator
      branch: release-1.26
    - repository: component-base
      branch: release-1.26
    - repository: kms
      branch: release-1.26
    source:
      branch: release-1.26
      dirs:
      - staging/src/k8s.io/apiextensions-apiserver
    required-packages:
    - k8s.io/code-generator
  - name: release-1.27
    go: 1.21.9
    dependencies:
    - repository: apimachinery
      branch: release-1.27
    - repository: api
      branch: release-1.27
    - repository: client-go
      branch: release-1.27
    - repository: apiserver
      branch: release-1.27
    - repository: code-generator
      branch: release-1.27
    - repository: component-base
      branch: release-1.27
    - repository: kms
      branch: release-1.27
    source:
      branch: release-1.27
      dirs:
      - staging/src/k8s.io/apiextensions-apiserver
    required-packages:
    - k8s.io/code-generator
  - name: release-1.28
    go: 1.21.9
    dependencies:
    - repository: apimachinery
      branch: release-1.28
    - repository: api
      branch: release-1.28
    - repository: client-go
      branch: release-1.28
    - repository: apiserver
      branch: release-1.28
    - repository: code-generator
      branch: release-1.28
    - repository: component-base
      branch: release-1.28
    - repository: kms
      branch: release-1.28
    source:
      branch: release-1.28
      dirs:
      - staging/src/k8s.io/apiextensions-apiserver
    required-packages:
    - k8s.io/code-generator
  - name: release-1.29
    go: 1.21.9
    dependencies:
    - repository: apimachinery
      branch: release-1.29
    - repository: api
      branch: release-1.29
    - repository: client-go
      branch: release-1.29
    - repository: apiserver
      branch: release-1.29
    - repository: code-generator
      branch: release-1.29
    - repository: component-base
      branch: release-1.29
    - repository: kms
      branch: release-1.29
    source:
      branch: release-1.29
      dirs:
      - staging/src/k8s.io/apiextensions-apiserver
    required-packages:
    - k8s.io/code-generator
  - name: release-1.30
    go: 1.22.2
    dependencies:
    - repository: apimachinery
      branch: release-1.30
    - repository: api
      branch: release-1.30
    - repository: client-go
      branch: release-1.30
    - repository: apiserver
      branch: release-1.30
    - repository: code-generator
      branch: release-1.30
    - repository: component-base
      branch: release-1.30
    - repository: kms
      branch: release-1.30
    source:
      branch: release-1.30
      dirs:
      - staging/src/k8s.io/apiextensions-apiserver
    required-packages:
    - k8s.io/code-generator
- destination: metrics
  branches:
  - name: master
    dependencies:
    - repository: apimachinery
      branch: master
    - repository: api
      branch: master
    - repository: client-go
      branch: master
    - repository: code-generator
      branch: master
    source:
      branch: master
      dirs:
      - staging/src/k8s.io/metrics
  - name: release-1.26
    go: 1.21.9
    dependencies:
    - repository: apimachinery
      branch: release-1.26
    - repository: api
      branch: release-1.26
    - repository: client-go
      branch: release-1.26
    - repository: code-generator
      branch: release-1.26
    source:
      branch: release-1.26
      dirs:
      - staging/src/k8s.io/metrics
  - name: release-1.27
    go: 1.21.9
    dependencies:
    - repository: apimachinery
      branch: release-1.27
    - repository: api
      branch: release-1.27
    - repository: client-go
      branch: release-1.27
    - repository: code-generator
      branch: release-1.27
    source:
      branch: release-1.27
      dirs:
      - staging/src/k8s.io/metrics
  - name: release-1.28
    go: 1.21.9
    dependencies:
    - repository: apimachinery
      branch: release-1.28
    - repository: api
      branch: release-1.28
    - repository: client-go
      branch: release-1.28
    - repository: code-generator
      branch: release-1.28
    source:
      branch: release-1.28
      dirs:
      - staging/src/k8s.io/metrics
  - name: release-1.29
    go: 1.21.9
    dependencies:
    - repository: apimachinery
      branch: release-1.29
    - repository: api
      branch: release-1.29
    - repository: client-go
      branch: release-1.29
    - repository: code-generator
      branch: release-1.29
    source:
      branch: release-1.29
      dirs:
      - staging/src/k8s.io/metrics
  - name: release-1.30
    go: 1.22.2
    dependencies:
    - repository: apimachinery
      branch: release-1.30
    - repository: api
      branch: release-1.30
    - repository: client-go
      branch: release-1.30
    - repository: code-generator
      branch: release-1.30
    source:
      branch: release-1.30
      dirs:
      - staging/src/k8s.io/metrics
  library: true
- destination: cli-runtime
  branches:
  - name: master
    dependencies:
    - repository: api
      branch: master
    - repository: apimachinery
      branch: master
    - repository: client-go
      branch: master
    source:
      branch: master
      dirs:
      - staging/src/k8s.io/cli-runtime
  - name: release-1.26
    go: 1.21.9
    dependencies:
    - repository: api
      branch: release-1.26
    - repository: apimachinery
      branch: release-1.26
    - repository: client-go
      branch: release-1.26
    source:
      branch: release-1.26
      dirs:
      - staging/src/k8s.io/cli-runtime
  - name: release-1.27
    go: 1.21.9
    dependencies:
    - repository: api
      branch: release-1.27
    - repository: apimachinery
      branch: release-1.27
    - repository: client-go
      branch: release-1.27
    source:
      branch: release-1.27
      dirs:
      - staging/src/k8s.io/cli-runtime
  - name: release-1.28
    go: 1.21.9
    dependencies:
    - repository: api
      branch: release-1.28
    - repository: apimachinery
      branch: release-1.28
    - repository: client-go
      branch: release-1.28
    source:
      branch: release-1.28
      dirs:
      - staging/src/k8s.io/cli-runtime
  - name: release-1.29
    go: 1.21.9
    dependencies:
    - repository: api
      branch: release-1.29
    - repository: apimachinery
      branch: release-1.29
    - repository: client-go
      branch: release-1.29
    source:
      branch: release-1.29
      dirs:
      - staging/src/k8s.io/cli-runtime
  - name: release-1.30
    go: 1.22.2
    dependencies:
    - repository: api
      branch: release-1.30
    - repository: apimachinery
      branch: release-1.30
    - repository: client-go
      branch: release-1.30
    source:
      branch: release-1.30
      dirs:
      - staging/src/k8s.io/cli-runtime
  library: true
- destination: sample-cli-plugin
  branches:
  - name: master
    dependencies:
    - repository: api
      branch: master
    - repository: apimachinery
      branch: master
    - repository: cli-runtime
      branch: master
    - repository: client-go
      branch: master
    source:
      branch: master
      dirs:
      - staging/src/k8s.io/sample-cli-plugin
  - name: release-1.26
    go: 1.21.9
    dependencies:
    - repository: api
      branch: release-1.26
    - repository: apimachinery
      branch: release-1.26
    - repository: cli-runtime
      branch: release-1.26
    - repository: client-go
      branch: release-1.26
    source:
      branch: release-1.26
      dirs:
      - staging/src/k8s.io/sample-cli-plugin
  - name: release-1.27
    go: 1.21.9
    dependencies:
    - repository: api
      branch: release-1.27
    - repository: apimachinery
      branch: release-1.27
    - repository: cli-runtime
      branch: release-1.27
    - repository: client-go
      branch: release-1.27
    source:
      branch: release-1.27
      dirs:
      - staging/src/k8s.io/sample-cli-plugin
  - name: release-1.28
    go: 1.21.9
    dependencies:
    - repository: api
      branch: release-1.28
    - repository: apimachinery
      branch: release-1.28
    - repository: cli-runtime
      branch: release-1.28
    - repository: client-go
      branch: release-1.28
    source:
      branch: release-1.28
      dirs:
      - staging/src/k8s.io/sample-cli-plugin
  - name: release-1.29
    go: 1.21.9
    dependencies:
    - repository: api
      branch: release-1.29
    - repository: apimachinery
      branch: release-1.29
    - repository: cli-runtime
      branch: release-1.29
    - repository: client-go
      branch: release-1.29
    source:
      branch: release-1.29
      dirs:
      - staging/src/k8s.io/sample-cli-plugin
  - name: release-1.30
    go: 1.22.2
    dependencies:
    - repository: api
      branch: release-1.30
    - repository: apimachinery
      branch: release-1.30
    - repository: cli-runtime
      branch: release-1.30
    - repository: client-go
      branch: release-1.30
    source:
      branch: release-1.30
      dirs:
      - staging/src/k8s.io/sample-cli-plugin
- destination: kube-proxy
  branches:
  - name: master
    dependencies:
    - repository: apimachinery
      branch: master
    - repository: component-base
      branch: master
    - repository: api
      branch: master
    - repository: client-go
      branch: master
    source:
      branch: master
      dirs:
      - staging/src/k8s.io/kube-proxy
  - name: release-1.26
    go: 1.21.9
    dependencies:
    - repository: apimachinery
      branch: release-1.26
    - repository: component-base
      branch: release-1.26
    - repository: api
      branch: release-1.26
    - repository: client-go
      branch: release-1.26
    source:
      branch: release-1.26
      dirs:
      - staging/src/k8s.io/kube-proxy
  - name: release-1.27
    go: 1.21.9
    dependencies:
    - repository: apimachinery
      branch: release-1.27
    - repository: component-base
      branch: release-1.27
    - repository: api
      branch: release-1.27
    - repository: client-go
      branch: release-1.27
    source:
      branch: release-1.27
      dirs:
      - staging/src/k8s.io/kube-proxy
  - name: release-1.28
    go: 1.21.9
    dependencies:
    - repository: apimachinery
      branch: release-1.28
    - repository: component-base
      branch: release-1.28
    - repository: api
      branch: release-1.28
    - repository: client-go
      branch: release-1.28
    source:
      branch: release-1.28
      dirs:
      - staging/src/k8s.io/kube-proxy
  - name: release-1.29
    go: 1.21.9
    dependencies:
    - repository: apimachinery
      branch: release-1.29
    - repository: component-base
      branch: release-1.29
    - repository: api
      branch: release-1.29
    - repository: client-go
      branch: release-1.29
    source:
      branch: release-1.29
      dirs:
      - staging/src/k8s.io/kube-proxy
  - name: release-1.30
    go: 1.22.2
    dependencies:
    - repository: apimachinery
      branch: release-1.30
    - repository: component-base
      branch: release-1.30
    - repository: api
      branch: release-1.30
    - repository: client-go
      branch: release-1.30
    source:
      branch: release-1.30
      dirs:
      - staging/src/k8s.io/kube-proxy
  library: true
- destination: cri-api
  branches:
  - name: master
    source:
      branch: master
      dirs:
      - staging/src/k8s.io/cri-api
  - name: release-1.26
    go: 1.21.9
    source:
      branch: release-1.26
      dirs:
      - staging/src/k8s.io/cri-api
  - name: release-1.27
    go: 1.21.9
    source:
      branch: release-1.27
      dirs:
      - staging/src/k8s.io/cri-api
  - name: release-1.28
    go: 1.21.9
    source:
      branch: release-1.28
      dirs:
      - staging/src/k8s.io/cri-api
  - name: release-1.29
    go: 1.21.9
    source:
      branch: release-1.29
      dirs:
      - staging/src/k8s.io/cri-api
  - name: release-1.30
    go: 1.22.2
    source:
      branch: release-1.30
      dirs:
      - staging/src/k8s.io/cri-api
  library: true
- destination: kubelet
  branches:
  - name: master
    dependencies:
    - repository: apimachinery
      branch: master
    - repository: apiserver
      branch: master
    - repository: api
      branch: master
    - repository: client-go
      branch: master
    - repository: cri-api
      branch: master
    - repository: component-base
      branch: master
    - repository: kms
      branch: master
    source:
      branch: master
      dirs:
      - staging/src/k8s.io/kubelet
  - name: release-1.26
    go: 1.21.9
    dependencies:
    - repository: apimachinery
      branch: release-1.26
    - repository: api
      branch: release-1.26
    - repository: client-go
      branch: release-1.26
    - repository: component-base
      branch: release-1.26
    source:
      branch: release-1.26
      dirs:
      - staging/src/k8s.io/kubelet
  - name: release-1.27
    go: 1.21.9
    dependencies:
    - repository: apimachinery
      branch: release-1.27
    - repository: api
      branch: release-1.27
    - repository: client-go
      branch: release-1.27
    - repository: component-base
      branch: release-1.27
    source:
      branch: release-1.27
      dirs:
      - staging/src/k8s.io/kubelet
  - name: release-1.28
    go: 1.21.9
    dependencies:
    - repository: apimachinery
      branch: release-1.28
    - repository: apiserver
      branch: release-1.28
    - repository: api
      branch: release-1.28
    - repository: client-go
      branch: release-1.28
    - repository: cri-api
      branch: release-1.28
    - repository: component-base
      branch: release-1.28
    - repository: kms
      branch: release-1.28
    source:
      branch: release-1.28
      dirs:
      - staging/src/k8s.io/kubelet
  - name: release-1.29
    go: 1.21.9
    dependencies:
    - repository: apimachinery
      branch: release-1.29
    - repository: apiserver
      branch: release-1.29
    - repository: api
      branch: release-1.29
    - repository: client-go
      branch: release-1.29
    - repository: cri-api
      branch: release-1.29
    - repository: component-base
      branch: release-1.29
    - repository: kms
      branch: release-1.29
    source:
      branch: release-1.29
      dirs:
      - staging/src/k8s.io/kubelet
  - name: release-1.30
    go: 1.22.2
    dependencies:
    - repository: apimachinery
      branch: release-1.30
    - repository: apiserver
      branch: release-1.30
    - repository: api
      branch: release-1.30
    - repository: client-go
      branch: release-1.30
    - repository: cri-api
      branch: release-1.30
    - repository: component-base
      branch: release-1.30
    - repository: kms
      branch: release-1.30
    source:
      branch: release-1.30
      dirs:
      - staging/src/k8s.io/kubelet
  library: true
- destination: kube-scheduler
  branches:
  - name: master
    dependencies:
    - repository: apimachinery
      branch: master
    - repository: component-base
      branch: master
    - repository: api
      branch: master
    - repository: client-go
      branch: master
    source:
      branch: master
      dirs:
      - staging/src/k8s.io/kube-scheduler
  - name: release-1.26
    go: 1.21.9
    dependencies:
    - repository: apimachinery
      branch: release-1.26
    - repository: component-base
      branch: release-1.26
    - repository: api
      branch: release-1.26
    - repository: client-go
      branch: release-1.26
    source:
      branch: release-1.26
      dirs:
      - staging/src/k8s.io/kube-scheduler
  - name: release-1.27
    go: 1.21.9
    dependencies:
    - repository: apimachinery
      branch: release-1.27
    - repository: component-base
      branch: release-1.27
    - repository: api
      branch: release-1.27
    - repository: client-go
      branch: release-1.27
    source:
      branch: release-1.27
      dirs:
      - staging/src/k8s.io/kube-scheduler
  - name: release-1.28
    go: 1.21.9
    dependencies:
    - repository: apimachinery
      branch: release-1.28
    - repository: component-base
      branch: release-1.28
    - repository: api
      branch: release-1.28
    - repository: client-go
      branch: release-1.28
    source:
      branch: release-1.28
      dirs:
      - staging/src/k8s.io/kube-scheduler
  - name: release-1.29
    go: 1.21.9
    dependencies:
    - repository: apimachinery
      branch: release-1.29
    - repository: component-base
      branch: release-1.29
    - repository: api
      branch: release-1.29
    - repository: client-go
      branch: release-1.29
    source:
      branch: release-1.29
      dirs:
      - staging/src/k8s.io/kube-scheduler
  - name: release-1.30
    go: 1.22.2
    dependencies:
    - repository: apimachinery
      branch: release-1.30
    - repository: component-base
      branch: release-1.30
    - repository: api
      branch: release-1.30
    - repository: client-go
      branch: release-1.30
    source:
      branch: release-1.30
      dirs:
      - staging/src/k8s.io/kube-scheduler
  library: true
- destination: controller-manager
  branches:
  - name: master
    dependencies:
    - repository: api
      branch: master
    - repository: apimachinery
      branch: master
    - repository: client-go
      branch: master
    - repository: component-base
      branch: master
    - repository: apiserver
      branch: master
    - repository: kms
      branch: master
    source:
      branch: master
      dirs:
      - staging/src/k8s.io/controller-manager
  - name: release-1.26
    go: 1.21.9
    dependencies:
    - repository: api
      branch: release-1.26
    - repository: apimachinery
      branch: release-1.26
    - repository: client-go
      branch: release-1.26
    - repository: component-base
      branch: release-1.26
    - repository: apiserver
      branch: release-1.26
    - repository: kms
      branch: release-1.26
    source:
      branch: release-1.26
      dirs:
      - staging/src/k8s.io/controller-manager
  - name: release-1.27
    go: 1.21.9
    dependencies:
    - repository: api
      branch: release-1.27
    - repository: apimachinery
      branch: release-1.27
    - repository: client-go
      branch: release-1.27
    - repository: component-base
      branch: release-1.27
    - repository: apiserver
      branch: release-1.27
    - repository: kms
      branch: release-1.27
    source:
      branch: release-1.27
      dirs:
      - staging/src/k8s.io/controller-manager
  - name: release-1.28
    go: 1.21.9
    dependencies:
    - repository: api
      branch: release-1.28
    - repository: apimachinery
      branch: release-1.28
    - repository: client-go
      branch: release-1.28
    - repository: component-base
      branch: release-1.28
    - repository: apiserver
      branch: release-1.28
    - repository: kms
      branch: release-1.28
    source:
      branch: release-1.28
      dirs:
      - staging/src/k8s.io/controller-manager
  - name: release-1.29
    go: 1.21.9
    dependencies:
    - repository: api
      branch: release-1.29
    - repository: apimachinery
      branch: release-1.29
    - repository: client-go
      branch: release-1.29
    - repository: component-base
      branch: release-1.29
    - repository: apiserver
      branch: release-1.29
    - repository: kms
      branch: release-1.29
    source:
      branch: release-1.29
      dirs:
      - staging/src/k8s.io/controller-manager
  - name: release-1.30
    go: 1.22.2
    dependencies:
    - repository: api
      branch: release-1.30
    - repository: apimachinery
      branch: release-1.30
    - repository: client-go
      branch: release-1.30
    - repository: component-base
      branch: release-1.30
    - repository: apiserver
      branch: release-1.30
    - repository: kms
      branch: release-1.30
    source:
      branch: release-1.30
      dirs:
      - staging/src/k8s.io/controller-manager
  library: true
- destination: cloud-provider
  branches:
  - name: master
    dependencies:
    - repository: api
      branch: master
    - repository: apimachinery
      branch: master
    - repository: apiserver
      branch: master
    - repository: client-go
      branch: master
    - repository: component-base
      branch: master
    - repository: controller-manager
      branch: master
    - repository: component-helpers
      branch: master
    - repository: kms
      branch: master
    source:
      branch: master
      dirs:
      - staging/src/k8s.io/cloud-provider
  - name: release-1.26
    go: 1.21.9
    dependencies:
    - repository: api
      branch: release-1.26
    - repository: apimachinery
      branch: release-1.26
    - repository: apiserver
      branch: release-1.26
    - repository: client-go
      branch: release-1.26
    - repository: component-base
      branch: release-1.26
    - repository: controller-manager
      branch: release-1.26
    - repository: component-helpers
      branch: release-1.26
    - repository: kms
      branch: release-1.26
    source:
      branch: release-1.26
      dirs:
      - staging/src/k8s.io/cloud-provider
  - name: release-1.27
    go: 1.21.9
    dependencies:
    - repository: api
      branch: release-1.27
    - repository: apimachinery
      branch: release-1.27
    - repository: apiserver
      branch: release-1.27
    - repository: client-go
      branch: release-1.27
    - repository: component-base
      branch: release-1.27
    - repository: controller-manager
      branch: release-1.27
    - repository: component-helpers
      branch: release-1.27
    - repository: kms
      branch: release-1.27
    source:
      branch: release-1.27
      dirs:
      - staging/src/k8s.io/cloud-provider
  - name: release-1.28
    go: 1.21.9
    dependencies:
    - repository: api
      branch: release-1.28
    - repository: apimachinery
      branch: release-1.28
    - repository: apiserver
      branch: release-1.28
    - repository: client-go
      branch: release-1.28
    - repository: component-base
      branch: release-1.28
    - repository: controller-manager
      branch: release-1.28
    - repository: component-helpers
      branch: release-1.28
    - repository: kms
      branch: release-1.28
    source:
      branch: release-1.28
      dirs:
      - staging/src/k8s.io/cloud-provider
  - name: release-1.29
    go: 1.21.9
    dependencies:
    - repository: api
      branch: release-1.29
    - repository: apimachinery
      branch: release-1.29
    - repository: apiserver
      branch: release-1.29
    - repository: client-go
      branch: release-1.29
    - repository: component-base
      branch: release-1.29
    - repository: controller-manager
      branch: release-1.29
    - repository: component-helpers
      branch: release-1.29
    - repository: kms
      branch: release-1.29
    source:
      branch: release-1.29
      dirs:
      - staging/src/k8s.io/cloud-provider
  - name: release-1.30
    go: 1.22.2
    dependencies:
    - repository: api
      branch: release-1.30
    - repository: apimachinery
      branch: release-1.30
    - repository: apiserver
      branch: release-1.30
    - repository: client-go
      branch: release-1.30
    - repository: component-base
      branch: release-1.30
    - repository: controller-manager
      branch: release-1.30
    - repository: component-helpers
      branch: release-1.30
    - repository: kms
      branch: release-1.30
    source:
      branch: release-1.30
      dirs:
      - staging/src/k8s.io/cloud-provider
  library: true
- destination: kube-controller-manager
  branches:
  - name: master
    dependencies:
    - repository: apimachinery
      branch: master
    - repository: apiserver
      branch: master
    - repository: component-base
      branch: master
    - repository: api
      branch: master
    - repository: client-go
      branch: master
    - repository: controller-manager
      branch: master
    - repository: cloud-provider
      branch: master
    - repository: component-helpers
      branch: master
    - repository: kms
      branch: master
    source:
      branch: master
      dirs:
      - staging/src/k8s.io/kube-controller-manager
  - name: release-1.26
    go: 1.21.9
    dependencies:
    - repository: apimachinery
      branch: release-1.26
    - repository: apiserver
      branch: release-1.26
    - repository: component-base
      branch: release-1.26
    - repository: api
      branch: release-1.26
    - repository: client-go
      branch: release-1.26
    - repository: controller-manager
      branch: release-1.26
    - repository: cloud-provider
      branch: release-1.26
    - repository: component-helpers
      branch: release-1.26
    - repository: kms
      branch: release-1.26
    source:
      branch: release-1.26
      dirs:
      - staging/src/k8s.io/kube-controller-manager
  - name: release-1.27
    go: 1.21.9
    dependencies:
    - repository: apimachinery
      branch: release-1.27
    - repository: apiserver
      branch: release-1.27
    - repository: component-base
      branch: release-1.27
    - repository: api
      branch: release-1.27
    - repository: client-go
      branch: release-1.27
    - repository: controller-manager
      branch: release-1.27
    - repository: cloud-provider
      branch: release-1.27
    - repository: component-helpers
      branch: release-1.27
    - repository: kms
      branch: release-1.27
    source:
      branch: release-1.27
      dirs:
      - staging/src/k8s.io/kube-controller-manager
  - name: release-1.28
    go: 1.21.9
    dependencies:
    - repository: apimachinery
      branch: release-1.28
    - repository: apiserver
      branch: release-1.28
    - repository: component-base
      branch: release-1.28
    - repository: api
      branch: release-1.28
    - repository: client-go
      branch: release-1.28
    - repository: controller-manager
      branch: release-1.28
    - repository: cloud-provider
      branch: release-1.28
    - repository: component-helpers
      branch: release-1.28
    - repository: kms
      branch: release-1.28
    source:
      branch: release-1.28
      dirs:
      - staging/src/k8s.io/kube-controller-manager
  - name: release-1.29
    go: 1.21.9
    dependencies:
    - repository: apimachinery
      branch: release-1.29
    - repository: apiserver
      branch: release-1.29
    - repository: component-base
      branch: release-1.29
    - repository: api
      branch: release-1.29
    - repository: client-go
      branch: release-1.29
    - repository: controller-manager
      branch: release-1.29
    - repository: cloud-provider
      branch: release-1.29
    - repository: component-helpers
      branch: release-1.29
    - repository: kms
      branch: release-1.29
    source:
      branch: release-1.29
      dirs:
      - staging/src/k8s.io/kube-controller-manager
  - name: release-1.30
    go: 1.22.2
    dependencies:
    - repository: apimachinery
      branch: release-1.30
    - repository: apiserver
      branch: release-1.30
    - repository: component-base
      branch: release-1.30
    - repository: api
      branch: release-1.30
    - repository: client-go
      branch: release-1.30
    - repository: controller-manager
      branch: release-1.30
    - repository: cloud-provider
      branch: release-1.30
    - repository: component-helpers
      branch: release-1.30
    - repository: kms
      branch: release-1.30
    source:
      branch: release-1.30
      dirs:
      - staging/src/k8s.io/kube-controller-manager
  library: true
- destination: cluster-bootstrap
  branches:
  - name: master
    dependencies:
    - repository: apimachinery
      branch: master
    - repository: api
      branch: master
    source:
      branch: master
      dirs:
      - staging/src/k8s.io/cluster-bootstrap
  - name: release-1.26
    go: 1.21.9
    dependencies:
    - repository: apimachinery
      branch: release-1.26
    - repository: api
      branch: release-1.26
    source:
      branch: release-1.26
      dirs:
      - staging/src/k8s.io/cluster-bootstrap
  - name: release-1.27
    go: 1.21.9
    dependencies:
    - repository: apimachinery
      branch: release-1.27
    - repository: api
      branch: release-1.27
    source:
      branch: release-1.27
      dirs:
      - staging/src/k8s.io/cluster-bootstrap
  - name: release-1.28
    go: 1.21.9
    dependencies:
    - repository: apimachinery
      branch: release-1.28
    - repository: api
      branch: release-1.28
    source:
      branch: release-1.28
      dirs:
      - staging/src/k8s.io/cluster-bootstrap
  - name: release-1.29
    go: 1.21.9
    dependencies:
    - repository: apimachinery
      branch: release-1.29
    - repository: api
      branch: release-1.29
    source:
      branch: release-1.29
      dirs:
      - staging/src/k8s.io/cluster-bootstrap
  - name: release-1.30
    go: 1.22.2
    dependencies:
    - repository: apimachinery
      branch: release-1.30
    - repository: api
      branch: release-1.30
    source:
      branch: release-1.30
      dirs:
      - staging/src/k8s.io/cluster-bootstrap
  library: true
- destination: csi-translation-lib
  branches:
  - name: master
    dependencies:
    - repository: api
      branch: master
    - repository: apimachinery
      branch: master
    source:
      branch: master
      dirs:
      - staging/src/k8s.io/csi-translation-lib
  - name: release-1.26
    go: 1.21.9
    dependencies:
    - repository: api
      branch: release-1.26
    - repository: apimachinery
      branch: release-1.26
    source:
      branch: release-1.26
      dirs:
      - staging/src/k8s.io/csi-translation-lib
  - name: release-1.27
    go: 1.21.9
    dependencies:
    - repository: api
      branch: release-1.27
    - repository: apimachinery
      branch: release-1.27
    source:
      branch: release-1.27
      dirs:
      - staging/src/k8s.io/csi-translation-lib
  - name: release-1.28
    go: 1.21.9
    dependencies:
    - repository: api
      branch: release-1.28
    - repository: apimachinery
      branch: release-1.28
    source:
      branch: release-1.28
      dirs:
      - staging/src/k8s.io/csi-translation-lib
  - name: release-1.29
    go: 1.21.9
    dependencies:
    - repository: api
      branch: release-1.29
    - repository: apimachinery
      branch: release-1.29
    source:
      branch: release-1.29
      dirs:
      - staging/src/k8s.io/csi-translation-lib
  - name: release-1.30
    go: 1.22.2
    dependencies:
    - repository: api
      branch: release-1.30
    - repository: apimachinery
      branch: release-1.30
    source:
      branch: release-1.30
      dirs:
      - staging/src/k8s.io/csi-translation-lib
  library: true
- destination: mount-utils
  branches:
  - name: master
    source:
      branch: master
      dirs:
      - staging/src/k8s.io/mount-utils
  - name: release-1.26
    go: 1.21.9
    source:
      branch: release-1.26
      dirs:
      - staging/src/k8s.io/mount-utils
  - name: release-1.27
    go: 1.21.9
    source:
      branch: release-1.27
      dirs:
      - staging/src/k8s.io/mount-utils
  - name: release-1.28
    go: 1.21.9
    source:
      branch: release-1.28
      dirs:
      - staging/src/k8s.io/mount-utils
  - name: release-1.29
    go: 1.21.9
    source:
      branch: release-1.29
      dirs:
      - staging/src/k8s.io/mount-utils
  - name: release-1.30
    go: 1.22.2
    source:
      branch: release-1.30
      dirs:
      - staging/src/k8s.io/mount-utils
  library: true
- destination: legacy-cloud-providers
  branches:
  - name: master
    dependencies:
    - repository: api
      branch: master
    - repository: apimachinery
      branch: master
    - repository: client-go
      branch: master
    - repository: cloud-provider
      branch: master
    - repository: apiserver
      branch: master
    - repository: component-base
      branch: master
    - repository: controller-manager
      branch: master
    - repository: component-helpers
      branch: master
    - repository: kms
      branch: master
    source:
      branch: master
      dirs:
      - staging/src/k8s.io/legacy-cloud-providers
  - name: release-1.26
    go: 1.21.9
    dependencies:
    - repository: api
      branch: release-1.26
    - repository: apimachinery
      branch: release-1.26
    - repository: client-go
      branch: release-1.26
    - repository: cloud-provider
      branch: release-1.26
    - repository: csi-translation-lib
      branch: release-1.26
    - repository: apiserver
      branch: release-1.26
    - repository: component-base
      branch: release-1.26
    - repository: controller-manager
      branch: release-1.26
    - repository: mount-utils
      branch: release-1.26
    - repository: component-helpers
      branch: release-1.26
    - repository: kms
      branch: release-1.26
    source:
      branch: release-1.26
      dirs:
      - staging/src/k8s.io/legacy-cloud-providers
  - name: release-1.27
    go: 1.21.9
    dependencies:
    - repository: api
      branch: release-1.27
    - repository: apimachinery
      branch: release-1.27
    - repository: client-go
      branch: release-1.27
    - repository: cloud-provider
      branch: release-1.27
    - repository: apiserver
      branch: release-1.27
    - repository: component-base
      branch: release-1.27
    - repository: controller-manager
      branch: release-1.27
    - repository: component-helpers
      branch: release-1.27
    - repository: kms
      branch: release-1.27
    source:
      branch: release-1.27
      dirs:
      - staging/src/k8s.io/legacy-cloud-providers
  - name: release-1.28
    go: 1.21.9
    dependencies:
    - repository: api
      branch: release-1.28
    - repository: apimachinery
      branch: release-1.28
    - repository: client-go
      branch: release-1.28
    - repository: cloud-provider
      branch: release-1.28
    - repository: apiserver
      branch: release-1.28
    - repository: component-base
      branch: release-1.28
    - repository: controller-manager
      branch: release-1.28
    - repository: component-helpers
      branch: release-1.28
    - repository: kms
      branch: release-1.28
    source:
      branch: release-1.28
      dirs:
      - staging/src/k8s.io/legacy-cloud-providers
  - name: release-1.29
    go: 1.21.9
    dependencies:
    - repository: api
      branch: release-1.29
    - repository: apimachinery
      branch: release-1.29
    - repository: client-go
      branch: release-1.29
    - repository: cloud-provider
      branch: release-1.29
    - repository: apiserver
      branch: release-1.29
    - repository: component-base
      branch: release-1.29
    - repository: controller-manager
      branch: release-1.29
    - repository: component-helpers
      branch: release-1.29
    - repository: kms
      branch: release-1.29
    source:
      branch: release-1.29
      dirs:
      - staging/src/k8s.io/legacy-cloud-providers
  - name: release-1.30
    go: 1.22.2
    dependencies:
    - repository: api
      branch: release-1.30
    - repository: apimachinery
      branch: release-1.30
    - repository: client-go
      branch: release-1.30
    - repository: cloud-provider
      branch: release-1.30
    - repository: apiserver
      branch: release-1.30
    - repository: component-base
      branch: release-1.30
    - repository: controller-manager
      branch: release-1.30
    - repository: component-helpers
      branch: release-1.30
    - repository: kms
      branch: release-1.30
    source:
      branch: release-1.30
      dirs:
      - staging/src/k8s.io/legacy-cloud-providers
  library: true
- destination: kubectl
  branches:
  - name: master
    dependencies:
    - repository: api
      branch: master
    - repository: apimachinery
      branch: master
    - repository: cli-runtime
      branch: master
    - repository: client-go
      branch: master
    - repository: code-generator
      branch: master
    - repository: component-base
      branch: master
    - repository: component-helpers
      branch: master
    - repository: metrics
      branch: master
    source:
      branch: master
      dirs:
      - staging/src/k8s.io/kubectl
  - name: release-1.26
    go: 1.21.9
    dependencies:
    - repository: api
      branch: release-1.26
    - repository: apimachinery
      branch: release-1.26
    - repository: cli-runtime
      branch: release-1.26
    - repository: client-go
      branch: release-1.26
    - repository: code-generator
      branch: release-1.26
    - repository: component-base
      branch: release-1.26
    - repository: component-helpers
      branch: release-1.26
    - repository: metrics
      branch: release-1.26
    source:
      branch: release-1.26
      dirs:
      - staging/src/k8s.io/kubectl
  - name: release-1.27
    go: 1.21.9
    dependencies:
    - repository: api
      branch: release-1.27
    - repository: apimachinery
      branch: release-1.27
    - repository: cli-runtime
      branch: release-1.27
    - repository: client-go
      branch: release-1.27
    - repository: code-generator
      branch: release-1.27
    - repository: component-base
      branch: release-1.27
    - repository: component-helpers
      branch: release-1.27
    - repository: metrics
      branch: release-1.27
    source:
      branch: release-1.27
      dirs:
      - staging/src/k8s.io/kubectl
  - name: release-1.28
    go: 1.21.9
    dependencies:
    - repository: api
      branch: release-1.28
    - repository: apimachinery
      branch: release-1.28
    - repository: cli-runtime
      branch: release-1.28
    - repository: client-go
      branch: release-1.28
    - repository: code-generator
      branch: release-1.28
    - repository: component-base
      branch: release-1.28
    - repository: component-helpers
      branch: release-1.28
    - repository: metrics
      branch: release-1.28
    source:
      branch: release-1.28
      dirs:
      - staging/src/k8s.io/kubectl
  - name: release-1.29
    go: 1.21.9
    dependencies:
    - repository: api
      branch: release-1.29
    - repository: apimachinery
      branch: release-1.29
    - repository: cli-runtime
      branch: release-1.29
    - repository: client-go
      branch: release-1.29
    - repository: code-generator
      branch: release-1.29
    - repository: component-base
      branch: release-1.29
    - repository: component-helpers
      branch: release-1.29
    - repository: metrics
      branch: release-1.29
    source:
      branch: release-1.29
      dirs:
      - staging/src/k8s.io/kubectl
  - name: release-1.30
    go: 1.22.2
    dependencies:
    - repository: api
      branch: release-1.30
    - repository: apimachinery
      branch: release-1.30
    - repository: cli-runtime
      branch: release-1.30
    - repository: client-go
      branch: release-1.30
    - repository: code-generator
      branch: release-1.30
    - repository: component-base
      branch: release-1.30
    - repository: component-helpers
      branch: release-1.30
    - repository: metrics
      branch: release-1.30
    source:
      branch: release-1.30
      dirs:
      - staging/src/k8s.io/kubectl
  library: true
- destination: pod-security-admission
  branches:
  - name: master
    dependencies:
    - repository: api
      branch: master
    - repository: apimachinery
      branch: master
    - repository: apiserver
      branch: master
    - repository: client-go
      branch: master
    - repository: component-base
      branch: master
    - repository: kms
      branch: master
    source:
      branch: master
      dirs:
      - staging/src/k8s.io/pod-security-admission
  - name: release-1.26
    go: 1.21.9
    dependencies:
    - repository: api
      branch: release-1.26
    - repository: apimachinery
      branch: release-1.26
    - repository: apiserver
      branch: release-1.26
    - repository: client-go
      branch: release-1.26
    - repository: component-base
      branch: release-1.26
    - repository: kms
      branch: release-1.26
    source:
      branch: release-1.26
      dirs:
      - staging/src/k8s.io/pod-security-admission
  - name: release-1.27
    go: 1.21.9
    dependencies:
    - repository: api
      branch: release-1.27
    - repository: apimachinery
      branch: release-1.27
    - repository: apiserver
      branch: release-1.27
    - repository: client-go
      branch: release-1.27
    - repository: component-base
      branch: release-1.27
    - repository: kms
      branch: release-1.27
    source:
      branch: release-1.27
      dirs:
      - staging/src/k8s.io/pod-security-admission
  - name: release-1.28
    go: 1.21.9
    dependencies:
    - repository: api
      branch: release-1.28
    - repository: apimachinery
      branch: release-1.28
    - repository: apiserver
      branch: release-1.28
    - repository: client-go
      branch: release-1.28
    - repository: component-base
      branch: release-1.28
    - repository: kms
      branch: release-1.28
    source:
      branch: release-1.28
      dirs:
      - staging/src/k8s.io/pod-security-admission
  - name: release-1.29
    go: 1.21.9
    dependencies:
    - repository: api
      branch: release-1.29
    - repository: apimachinery
      branch: release-1.29
    - repository: apiserver
      branch: release-1.29
    - repository: client-go
      branch: release-1.29
    - repository: component-base
      branch: release-1.29
    - repository: kms
      branch: release-1.29
    source:
      branch: release-1.29
      dirs:
      - staging/src/k8s.io/pod-security-admission
  - name: release-1.30
    go: 1.22.2
    dependencies:
    - repository: api
      branch: release-1.30
    - repository: apimachinery
      branch: release-1.30
    - repository: apiserver
      branch: release-1.30
    - repository: client-go
      branch: release-1.30
    - repository: component-base
      branch: release-1.30
    - repository: kms
      branch: release-1.30
    source:
      branch: release-1.30
      dirs:
      - staging/src/k8s.io/pod-security-admission
  library: true
- destination: dynamic-resource-allocation
  branches:
  - name: master
    dependencies:
    - repository: apimachinery
      branch: master
    - repository: apiserver
      branch: master
    - repository: api
      branch: master
    - repository: client-go
      branch: master
    - repository: cri-api
      branch: master
    - repository: component-base
      branch: master
    - repository: kms
      branch: master
    - repository: kubelet
      branch: master
    source:
      branch: master
      dirs:
      - staging/src/k8s.io/dynamic-resource-allocation
  - name: release-1.26
    go: 1.21.9
    dependencies:
    - repository: apimachinery
      branch: release-1.26
    - repository: api
      branch: release-1.26
    - repository: client-go
      branch: release-1.26
    - repository: component-base
      branch: release-1.26
    - repository: kubelet
      branch: release-1.26
    source:
      branch: release-1.26
      dirs:
      - staging/src/k8s.io/dynamic-resource-allocation
  - name: release-1.27
    go: 1.21.9
    dependencies:
    - repository: apimachinery
      branch: release-1.27
    - repository: api
      branch: release-1.27
    - repository: client-go
      branch: release-1.27
    - repository: component-base
      branch: release-1.27
    - repository: kubelet
      branch: release-1.27
    source:
      branch: release-1.27
      dirs:
      - staging/src/k8s.io/dynamic-resource-allocation
  - name: release-1.28
    go: 1.21.9
    dependencies:
    - repository: apimachinery
      branch: release-1.28
    - repository: apiserver
      branch: release-1.28
    - repository: api
      branch: release-1.28
    - repository: client-go
      branch: release-1.28
    - repository: cri-api
      branch: release-1.28
    - repository: component-base
      branch: release-1.28
    - repository: kubelet
      branch: release-1.28
    source:
      branch: release-1.28
      dirs:
      - staging/src/k8s.io/dynamic-resource-allocation
  - name: release-1.29
    go: 1.21.9
    dependencies:
    - repository: apimachinery
      branch: release-1.29
    - repository: apiserver
      branch: release-1.29
    - repository: api
      branch: release-1.29
    - repository: client-go
      branch: release-1.29
    - repository: cri-api
      branch: release-1.29
    - repository: component-base
      branch: release-1.29
    - repository: kubelet
      branch: release-1.29
    source:
      branch: release-1.29
      dirs:
      - staging/src/k8s.io/dynamic-resource-allocation
  - name: release-1.30
    go: 1.22.2
    dependencies:
    - repository: apimachinery
      branch: release-1.30
    - repository: apiserver
      branch: release-1.30
    - repository: api
      branch: release-1.30
    - repository: client-go
      branch: release-1.30
    - repository: cri-api
      branch: release-1.30
    - repository: component-base
      branch: release-1.30
    - repository: kms
      branch: release-1.30
    - repository: kubelet
      branch: release-1.30
    source:
      branch: release-1.30
      dirs:
      - staging/src/k8s.io/dynamic-resource-allocation
- destination: endpointslice
  branches:
  - name: master
    dependencies:
    - repository: api
      branch: master
    - repository: apimachinery
      branch: master
    - repository: client-go
      branch: master
    - repository: component-base
      branch: master
    source:
      branch: master
      dirs:
      - staging/src/k8s.io/endpointslice
  - name: release-1.28
    go: 1.21.9
    dependencies:
    - repository: api
      branch: release-1.28
    - repository: apimachinery
      branch: release-1.28
    - repository: client-go
      branch: release-1.28
    - repository: component-base
      branch: release-1.28
    source:
      branch: release-1.28
      dirs:
      - staging/src/k8s.io/endpointslice
  - name: release-1.29
    go: 1.21.9
    dependencies:
    - repository: api
      branch: release-1.29
    - repository: apimachinery
      branch: release-1.29
    - repository: client-go
      branch: release-1.29
    - repository: component-base
      branch: release-1.29
    source:
      branch: release-1.29
      dirs:
      - staging/src/k8s.io/endpointslice
  - name: release-1.30
    go: 1.22.2
    dependencies:
    - repository: api
      branch: release-1.30
    - repository: apimachinery
      branch: release-1.30
    - repository: client-go
      branch: release-1.30
    - repository: component-base
      branch: release-1.30
    source:
      branch: release-1.30
      dirs:
      - staging/src/k8s.io/endpointslice
recursive-delete-patterns:
- '*/.gitattributes'
default-go-version: 1.22.2
