- baseImportPath: "./pkg/apis/core"
  allowedImports:
  - k8s.io/apimachinery
  - k8s.io/apiserver/pkg/util/feature
  - k8s.io/component-base/featuregate/testing
  - k8s.io/kubernetes/pkg/apis/core
  - k8s.io/kubernetes/pkg/api/v1/service
  - k8s.io/kubernetes/pkg/features
  - k8s.io/kubernetes/pkg/fieldpath
  - k8s.io/kubernetes/pkg/util
  - k8s.io/api/core/v1
  - k8s.io/utils/pointer
  - k8s.io/utils/net
  - k8s.io/klog

  # the following are temporary and should go away. Think twice (or more) before adding anything here.
  # Main goal: pkg/apis should be as self-contained as possible.
  - k8s.io/kubernetes/pkg/apis/apps
  - k8s.io/kubernetes/pkg/api/legacyscheme
  - k8s.io/api/apps/v1
  ignoredSubTrees:
  - "./pkg/apis/core/validation"

- baseImportPath: "./staging/src/k8s.io/cli-runtime/pkg/genericclioptions"
  allowedImports:
  - k8s.io/apimachinery
  - k8s.io/client-go
  # TODO this one should be tightened.  We depend on it for testing, but we should instead create our own scheme
  - k8s.io/api/core/v1
  - k8s.io/cli-runtime/pkg/genericiooptions
  - k8s.io/cli-runtime/pkg/printers
  - k8s.io/cli-runtime/pkg/resource
  - k8s.io/cli-runtime/pkg/kustomize
  - k8s.io/utils/pointer

- baseImportPath: "./staging/src/k8s.io/apimachinery"
  allowedImports:
  - k8s.io/apimachinery
  - k8s.io/kube-openapi
  - k8s.io/utils/clock
  - k8s.io/utils/net
  - k8s.io/utils/strings
  - k8s.io/klog

- baseImportPath: "./staging/src/k8s.io/api"
  allowedImports:
  - k8s.io/api
  - k8s.io/apimachinery
  - k8s.io/klog

- baseImportPath: "./staging/src/k8s.io/code-generator"
  ignoredSubTrees:
  - "./staging/src/k8s.io/code-generator/examples"
  - "./staging/src/k8s.io/code-generator/cmd/defaulter-gen/output_tests"
  allowedImports:
  - k8s.io/gengo
  - k8s.io/code-generator
  - k8s.io/kube-openapi
  - k8s.io/klog

- baseImportPath: "./staging/src/k8s.io/component-base"
  allowedImports:
  - k8s.io/apimachinery
  - k8s.io/component-base
  - k8s.io/client-go
  - k8s.io/client-go/tools/
  - k8s.io/klog
  - k8s.io/utils

- baseImportPath: "./staging/src/k8s.io/client-go"
  allowedImports:
  - k8s.io/api
  - k8s.io/apimachinery
  - k8s.io/client-go
  - k8s.io/klog
  - k8s.io/kube-openapi
  - k8s.io/utils

# prevent core machinery from taking explicit v1 references unless
# necessary
- baseImportPath: "./staging/src/k8s.io/client-go/rest"
  excludeTests: true
  allowedImports:
  - k8s.io/apimachinery
  - k8s.io/client-go
  - k8s.io/klog
  - k8s.io/utils
- baseImportPath: "./staging/src/k8s.io/client-go/tools"
  excludeTests: true
  ignoredSubTrees:
  - "./staging/src/k8s.io/client-go/tools/cache/testing"
  - "./staging/src/k8s.io/client-go/tools/leaderelection/resourcelock"
  - "./staging/src/k8s.io/client-go/tools/portforward"
  - "./staging/src/k8s.io/client-go/tools/record"
  - "./staging/src/k8s.io/client-go/tools/events"
  - "./staging/src/k8s.io/client-go/tools/reference"
  - "./staging/src/k8s.io/client-go/tools/remotecommand"
  allowedImports:
  - k8s.io/apimachinery
  - k8s.io/client-go
  - k8s.io/klog
  - k8s.io/utils

- baseImportPath: "./staging/src/k8s.io/apiserver"
  allowedImports:
  - k8s.io/api
  - k8s.io/apimachinery
  - k8s.io/apiserver
  - k8s.io/client-go
  - k8s.io/component-base
  - k8s.io/kube-openapi
  - k8s.io/utils
  - k8s.io/klog
  - k8s.io/kms

- baseImportPath: "./staging/src/k8s.io/metrics"
  allowedImports:
  - k8s.io/api
  - k8s.io/apimachinery
  - k8s.io/client-go
  - k8s.io/code-generator
  - k8s.io/metrics
  - k8s.io/klog

- baseImportPath: "./staging/src/k8s.io/kube-aggregator"
  allowedImports:
  - k8s.io/api
  - k8s.io/apimachinery
  - k8s.io/apiserver
  - k8s.io/client-go
  - k8s.io/code-generator
  - k8s.io/component-base
  - k8s.io/kube-aggregator
  - k8s.io/kube-openapi
  - k8s.io/klog
  - k8s.io/utils

- baseImportPath: "./staging/src/k8s.io/kubectl"
  allowedImports:
  - k8s.io/api
  - k8s.io/apimachinery
  - k8s.io/cli-runtime
  - k8s.io/client-go
  - k8s.io/component-base
  - k8s.io/component-helpers
  - k8s.io/kubectl
  - k8s.io/kube-openapi
  - k8s.io/metrics
  - k8s.io/utils
  - k8s.io/klog

- baseImportPath: "./staging/src/k8s.io/sample-apiserver"
  allowedImports:
  - k8s.io/api
  - k8s.io/apimachinery
  - k8s.io/apiserver
  - k8s.io/client-go
  - k8s.io/code-generator
  - k8s.io/component-base
  - k8s.io/kube-openapi
  - k8s.io/sample-apiserver
  - k8s.io/utils/net
  - k8s.io/klog

- baseImportPath: "./staging/src/k8s.io/apiextensions-apiserver"
  allowedImports:
  - k8s.io/api
  - k8s.io/apiextensions-apiserver
  - k8s.io/apimachinery
  - k8s.io/apiserver
  - k8s.io/client-go
  - k8s.io/code-generator
  - k8s.io/component-base
  - k8s.io/klog
  - k8s.io/kube-openapi
  - k8s.io/utils

- baseImportPath: "./vendor/k8s.io/kube-openapi"
  allowedImports:
  - k8s.io/kube-openapi
  - k8s.io/gengo
  - k8s.io/klog
  - k8s.io/utils

- baseImportPath: "./staging/src/k8s.io/sample-cli-plugin"
  allowedImports:
  - k8s.io/api
  - k8s.io/cli-runtime
  - k8s.io/client-go
  - k8s.io/sample-cli-plugin

- baseImportPath: "./staging/src/k8s.io/kube-controller-manager"
  allowedImports:
  - k8s.io/apimachinery
  - k8s.io/cloud-provider
  - k8s.io/component-base
  - k8s.io/controller-manager
  - k8s.io/klog
  - k8s.io/utils

- baseImportPath: "./staging/src/k8s.io/kube-proxy"
  allowedImports:
  - k8s.io/apimachinery
  - k8s.io/component-base
  - k8s.io/klog
  - k8s.io/utils

- baseImportPath: "./staging/src/k8s.io/kube-scheduler"
  allowedImports:
  - k8s.io/api
  - k8s.io/apimachinery
  - k8s.io/component-base
  - k8s.io/klog
  - k8s.io/kube-scheduler
  - k8s.io/utils

- baseImportPath: "./staging/src/k8s.io/kubelet"
  allowedImports:
  - k8s.io/api
  - k8s.io/apimachinery
  - k8s.io/apiserver
  - k8s.io/client-go
  - k8s.io/cri-api
  - k8s.io/klog
  - k8s.io/component-base
  - k8s.io/kubelet
  - k8s.io/utils

- baseImportPath: "./staging/src/k8s.io/cluster-bootstrap"
  allowedImports:
  - k8s.io/api
  - k8s.io/apimachinery
  - k8s.io/cluster-bootstrap
  - k8s.io/klog

- baseImportPath: "./staging/src/k8s.io/cloud-provider"
  allowedImports:
  - k8s.io/api
  - k8s.io/apimachinery
  - k8s.io/apiserver
  - k8s.io/client-go
  - k8s.io/cloud-provider
  - k8s.io/component-base
  - k8s.io/controller-manager
  - k8s.io/component-helpers
  - k8s.io/klog
  - k8s.io/utils

- baseImportPath: "./staging/src/k8s.io/dynamic-resource-allocation"
  allowedImports:
  - k8s.io/api
  - k8s.io/apimachinery
  - k8s.io/apiserver/pkg/apis/cel
  - k8s.io/apiserver/pkg/cel
  - k8s.io/apiserver/pkg/cel/environment
  - k8s.io/client-go
  - k8s.io/dynamic-resource-allocation
  - k8s.io/klog
  - k8s.io/kubelet
  - k8s.io/utils

- baseImportPath: "./staging/src/k8s.io/legacy-cloud-providers"
  allowedImports:
  - k8s.io/api
  - k8s.io/apimachinery
  - k8s.io/client-go
  - k8s.io/cloud-provider
  - k8s.io/csi-translation-lib
  - k8s.io/klog
  - k8s.io/legacy-cloud-providers
  - k8s.io/mount-utils
  - k8s.io/utils
  - k8s.io/apiserver/pkg/util/feature
  - k8s.io/component-base/featuregate
  - k8s.io/component-base/metrics
  - k8s.io/component-base/metrics/legacyregistry

- baseImportPath: "./staging/src/k8s.io/csi-translation-lib"
  allowedImports:
  - k8s.io/api
  - k8s.io/apimachinery
  - k8s.io/klog
  - k8s.io/csi-translation-lib

- baseImportPath: "./staging/src/k8s.io/component-helpers"
  allowedImports:
  - k8s.io/api
  - k8s.io/apimachinery
  - k8s.io/client-go
  - k8s.io/component-helpers
  - k8s.io/klog
  - k8s.io/utils

- baseImportPath: "./staging/src/k8s.io/pod-security-admission"
  allowedImports:
  - k8s.io/api
  - k8s.io/apimachinery
  - k8s.io/apiserver/pkg/admission
  - k8s.io/apiserver/pkg/server
  - k8s.io/client-go
  - k8s.io/component-base
  - k8s.io/klog
  - k8s.io/pod-security-admission
  - k8s.io/utils

- baseImportPath: "./staging/src/k8s.io/kms"
  allowedImports:
  - k8s.io/kms

- baseImportPath: "./staging/src/k8s.io/endpointslice"
  allowedImports:
  - k8s.io/api
  - k8s.io/apimachinery
  - k8s.io/client-go
  - k8s.io/component-base
  - k8s.io/endpointslice
  - k8s.io/klog
  - k8s.io/utils
