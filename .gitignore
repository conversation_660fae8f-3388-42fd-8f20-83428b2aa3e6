# OSX leaves these everywhere on SMB shares
._*

# OSX trash
.DS_Store

# Developers can store local stuff in dirs named __something
__*

# Eclipse files
.classpath
.project
.settings/**

# Files generated by JetBrains IDEs, e.g. IntelliJ IDEA
.idea/
*.iml

# Vscode files
.vscode

# This is where the result of the go build goes
/output*/
/_output*/
/_output

# Emacs save files
*~
\#*\#
.\#*

# Vim-related files
[._]*.s[a-w][a-z]
[._]s[a-w][a-z]
*.un~
Session.vim
.netrwhist

# cscope-related files
cscope.*

# Go test binaries
*.test
/hack/.test-cmd-auth

# JUnit test output from ginkgo e2e tests
/junit*.xml

# Mercurial files
**/.hg
**/.hg*

# Vagrant
.vagrant
network_closure.sh

# Local cluster env variables
/cluster/env.sh

# Compiled binaries in third_party
/third_party/pkg

# Also ignore etcd installed by hack/install-etcd.sh
/third_party/etcd*
/default.etcd

# Also ignore protoc installed by hack/install-protoc.sh
/third_party/protoc*

# User cluster configs
.kubeconfig

.tags*

# Version file for dockerized build
.dockerized-kube-version-defs

# Web UI
/www/master/node_modules/
/www/master/npm-debug.log
/www/master/shared/config/development.json

# Karma output
/www/test_out

# precommit temporary directories created by ./hack/verify-generated-docs.sh and ./hack/lib/util.sh
/_tmp/
/doc_tmp/

# Test artifacts produced by Prow/kubetest2 jobs
/_artifacts/
/_rundir/

# Go dependencies installed on Jenkins
/_gopath/

# Config directories created by gcloud and gsutil on Jenkins
/.config/gcloud*/
/.gsutil/

# CoreOS stuff
/cluster/libvirt-coreos/coreos_*.img

# Downloaded Kubernetes binary release
/kubernetes/

# direnv .envrc files
.envrc

# Downloaded kubernetes binary release tar ball
kubernetes.tar.gz

# Phony test files used as part of coverage generation
zz_generated_*_test.go

# Just in time generated data in the source, should never be committed
/test/e2e/generated/bindata.go

# This file used by some vendor repos (e.g. github.com/go-openapi/...) to store secret variables and should not be ignored
!\.drone\.sec

/bazel-*
*.pyc

# generated by verify-vendor.sh
vendordiff.patch
